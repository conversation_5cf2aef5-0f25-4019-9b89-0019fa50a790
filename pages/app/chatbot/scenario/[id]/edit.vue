<template>
  <div>
    <AppScenarioForm
      :scenario="scenario"
      :saving="saving"
      :end-templates="endTemplates"
      :loading-end-templates="loadingEndTemplates"
      @save="handleSave"
      @cancel="handleCancel"
    />
  </div>
</template>

<script setup lang="ts">
import type { Scenario } from '~/types'
import { useScenarioService } from '~/composables/useScenarioService'

definePageMeta({
  navigationPage: "app-chatbot-scenario-id-edit",
  middleware: ["permissions"],
})

const router = useRouter()
const route = useRoute()
const toast = useToast()

const {
  saving,
  endTemplates,
  loadingEndTemplates,
  getScenario,
  updateScenario,
  fetchEndTemplates
} = useScenarioService()

const scenarioId = route.params.id as string
const scenario = ref<Scenario | null>(null)

// Load scenario and end templates
const loadData = async () => {
  await Promise.all([
    loadScenario(),
    fetchEndTemplates()
  ])
}

const loadScenario = async () => {
  if (scenarioId) {
    scenario.value = await getScenario(scenarioId)
  }
}

await loadData()

const handleSave = async (scenarioData: Partial<Scenario>) => {
  try {
    const updatedScenario = await updateScenario(scenarioId, scenarioData)
    toast.add({
      title: 'シナリオを更新しました',
      description: `「${updatedScenario?.name}」を更新しました`,
      color: 'green'
    })
    router.push('/app/chatbot/scenario')
  } catch (error) {
    toast.add({
      title: 'エラー',
      description: 'シナリオの更新に失敗しました',
      color: 'red'
    })
  }
}

const handleCancel = () => {
  router.push(`/app/chatbot/scenario/${scenarioId}`)
}
</script>