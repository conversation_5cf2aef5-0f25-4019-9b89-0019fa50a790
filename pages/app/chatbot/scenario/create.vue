<template>
  <div>
    <AppScenarioForm
      :saving="saving"
      :end-templates="endTemplates"
      :loading-end-templates="loadingEndTemplates"
      @save="handleSave"
      @cancel="handleCancel"
    />
  </div>
</template>

<script setup lang="ts">
import type { Scenario } from '~/types'
import { useScenarioService } from '~/composables/useScenarioService'

definePageMeta({
  navigationPage: "app-chatbot-scenario-create",
  middleware: ["permissions"],
})

const router = useRouter()
const toast = useToast()

const {
  saving,
  endTemplates,
  loadingEndTemplates,
  createScenario,
  fetchEndTemplates
} = useScenarioService()

// Load end templates for the form
await fetchEndTemplates()

const handleSave = async (scenarioData: Partial<Scenario>) => {
  try {
    const newScenario = await createScenario(scenarioData)
    toast.add({
      title: 'シナリオを作成しました',
      description: `「${newScenario?.name}」を作成しました`,
      color: 'green'
    })
    router.push('/app/chatbot/scenario')
  } catch (error) {
    toast.add({
      title: 'エラー',
      description: 'シナリオの作成に失敗しました',
      color: 'red'
    })
  }
}

const handleCancel = () => {
  router.push('/app/chatbot/scenario')
}
</script>